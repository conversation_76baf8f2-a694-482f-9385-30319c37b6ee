#!/usr/bin/env python3

print("Testing imports...")

try:
    import cv2
    print(f"✅ OpenCV imported successfully - version: {cv2.__version__}")
except ImportError as e:
    print(f"❌ OpenCV import failed: {e}")

try:
    import torch
    print(f"✅ PyTorch imported successfully - version: {torch.__version__}")
    print(f"   CUDA available: {torch.cuda.is_available()}")
except ImportError as e:
    print(f"❌ PyTorch import failed: {e}")

try:
    from ultralytics import YOLO
    print("✅ Ultralytics YOLO imported successfully")
except ImportError as e:
    print(f"❌ Ultralytics import failed: {e}")

try:
    import streamlit as st
    print(f"✅ Streamlit imported successfully - version: {st.__version__}")
except ImportError as e:
    print(f"❌ Streamlit import failed: {e}")

print("\nAll imports tested!")
