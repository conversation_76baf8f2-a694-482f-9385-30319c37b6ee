# SIBI Sign Language Detection

Aplikasi real-time camera untuk mendeteksi bahasa isyarat SIBI (Sistem Isyarat Bahasa Indonesia) menggunakan YOLOv11 PyTorch model.

## Fitur

- 🎥 Real-time camera detection
- 🤟 Deteksi 9 kata SIBI: mau, saya, mana, makan, kamu, jalan, hotel, ke, di
- 🚀 GPU acceleration support
- 📊 Live statistics dan hasil deteksi
- ⚙️ Adjustable confidence threshold

## Instalasi

1. Clone atau download repository ini
2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Pastikan model `sibiv3.pt` ada di folder `models/`

## Cara Menjalankan

```bash
streamlit run app.py
```

Aplikasi akan terbuka di browser pada `http://localhost:8501`

## Penggunaan

1. Klik tombol "🎥 Start Camera" untuk memulai deteksi
2. Lakukan gerakan bahasa isyarat SIBI di depan camera
3. Aplikasi akan menampilkan:
   - Bounding box di sekitar tangan yang terdeteksi
   - Label kata yang dideteksi dengan confidence score
   - Statistik deteksi real-time
4. Klik "⏹️ Stop Camera" untuk menghentikan

## Pengaturan

- **Confidence Threshold**: Atur minimum confidence score untuk deteksi (default: 0.5)
- Camera akan otomatis menggunakan webcam default (index 0)

## Troubleshooting

### Camera tidak dapat diakses
- Pastikan webcam terhubung dan tidak digunakan aplikasi lain
- Coba restart aplikasi

### Model tidak dapat dimuat
- Pastikan file `models/sibiv3.pt` ada dan dapat diakses
- Periksa kompatibilitas versi PyTorch

### Performance lambat
- Aplikasi akan otomatis menggunakan GPU jika tersedia
- Untuk CPU, kurangi resolusi camera atau tingkatkan confidence threshold

## Sistem Requirements

- Python 3.8+
- Webcam
- GPU (opsional, untuk performa lebih baik)

## Model Information

Model yang digunakan adalah YOLOv11 yang telah dilatih untuk mendeteksi 9 kata bahasa isyarat SIBI:
1. mau
2. saya  
3. mana
4. makan
5. kamu
6. jalan
7. hotel
8. ke
9. di
