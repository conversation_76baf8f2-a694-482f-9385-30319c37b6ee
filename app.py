import streamlit as st
import cv2
import torch
import numpy as np
from PIL import Image
import time

# Konfigurasi halaman
st.set_page_config(
    page_title="SIBI Sign Language Detection",
    page_icon="🤟",
    layout="wide"
)

# Kelas-kelas SIBI yang dapat dideteksi
SIBI_CLASSES = ['mau', 'saya', 'mana', 'makan', 'kamu', 'jalan', 'hotel', 'ke', 'di']

@st.cache_resource
def load_model():
    """Load YOLOv11 model untuk deteksi SIBI"""
    try:
        # Load model PyTorch
        model = torch.load('models/sibiv3.pt', map_location='cpu')
        
        # Jika menggunakan GPU dan tersedia
        if torch.cuda.is_available():
            model = model.cuda()
            st.success("🚀 Model loaded with GPU acceleration!")
        else:
            st.info("💻 Model loaded with CPU")
            
        model.eval()
        return model
    except Exception as e:
        st.error(f"Error loading model: {str(e)}")
        return None

def preprocess_frame(frame):
    """Preprocessing frame untuk model"""
    # Resize frame ke ukuran yang diharapkan model (biasanya 640x640 untuk YOLO)
    img = cv2.resize(frame, (640, 640))
    
    # Convert BGR ke RGB
    img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    
    # Normalize ke [0,1]
    img = img.astype(np.float32) / 255.0
    
    # Convert ke tensor dan tambahkan batch dimension
    img_tensor = torch.from_numpy(img).permute(2, 0, 1).unsqueeze(0)
    
    return img_tensor

def detect_signs(model, frame):
    """Deteksi bahasa isyarat pada frame"""
    try:
        # Preprocess frame
        img_tensor = preprocess_frame(frame)
        
        # Pindahkan ke GPU jika tersedia
        if torch.cuda.is_available() and next(model.parameters()).is_cuda:
            img_tensor = img_tensor.cuda()
        
        # Inference
        with torch.no_grad():
            results = model(img_tensor)
        
        # Parse hasil deteksi (sesuaikan dengan format output model Anda)
        detections = []
        
        # Contoh parsing untuk YOLOv11 (sesuaikan dengan model Anda)
        if hasattr(results, 'pred') and results.pred[0] is not None:
            pred = results.pred[0].cpu().numpy()
            
            for detection in pred:
                if len(detection) >= 6:
                    x1, y1, x2, y2, conf, cls = detection[:6]
                    
                    if conf > 0.5:  # Threshold confidence
                        class_name = SIBI_CLASSES[int(cls)] if int(cls) < len(SIBI_CLASSES) else "unknown"
                        detections.append({
                            'bbox': [int(x1), int(y1), int(x2), int(y2)],
                            'confidence': float(conf),
                            'class': class_name
                        })
        
        return detections
    
    except Exception as e:
        st.error(f"Error during detection: {str(e)}")
        return []

def draw_detections(frame, detections):
    """Gambar bounding box dan label pada frame"""
    for det in detections:
        x1, y1, x2, y2 = det['bbox']
        conf = det['confidence']
        class_name = det['class']
        
        # Gambar bounding box
        cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
        
        # Label dengan confidence
        label = f"{class_name}: {conf:.2f}"
        label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2)[0]
        
        # Background untuk text
        cv2.rectangle(frame, (x1, y1 - label_size[1] - 10), 
                     (x1 + label_size[0], y1), (0, 255, 0), -1)
        
        # Text
        cv2.putText(frame, label, (x1, y1 - 5), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    
    return frame

def main():
    st.title("🤟 SIBI Sign Language Detection")
    st.markdown("Real-time detection untuk bahasa isyarat SIBI menggunakan YOLOv11")
    
    # Load model
    model = load_model()
    if model is None:
        st.stop()
    
    # Sidebar untuk pengaturan
    st.sidebar.header("⚙️ Pengaturan")
    confidence_threshold = st.sidebar.slider("Confidence Threshold", 0.1, 1.0, 0.5, 0.1)
    
    # Informasi kelas yang dapat dideteksi
    st.sidebar.header("📝 Kelas SIBI")
    st.sidebar.write("Kata yang dapat dideteksi:")
    for i, class_name in enumerate(SIBI_CLASSES, 1):
        st.sidebar.write(f"{i}. {class_name}")
    
    # Layout kolom
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.header("📹 Live Camera")
        
        # Placeholder untuk video
        video_placeholder = st.empty()
        
        # Tombol kontrol
        start_button = st.button("🎥 Start Camera", type="primary")
        stop_button = st.button("⏹️ Stop Camera")
    
    with col2:
        st.header("📊 Detection Results")
        results_placeholder = st.empty()
        
        # Statistik
        st.header("📈 Statistics")
        stats_placeholder = st.empty()
    
    # State untuk camera
    if 'camera_active' not in st.session_state:
        st.session_state.camera_active = False
    
    if start_button:
        st.session_state.camera_active = True
    
    if stop_button:
        st.session_state.camera_active = False
    
    # Main camera loop
    if st.session_state.camera_active:
        try:
            # Inisialisasi camera
            cap = cv2.VideoCapture(0)
            cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
            
            if not cap.isOpened():
                st.error("❌ Tidak dapat mengakses camera!")
                st.session_state.camera_active = False
                st.stop()
            
            detection_count = 0
            total_detections = {class_name: 0 for class_name in SIBI_CLASSES}
            
            while st.session_state.camera_active:
                ret, frame = cap.read()
                if not ret:
                    st.error("❌ Gagal membaca frame dari camera!")
                    break
                
                # Flip frame horizontal untuk mirror effect
                frame = cv2.flip(frame, 1)
                
                # Deteksi bahasa isyarat
                detections = detect_signs(model, frame)
                
                # Filter berdasarkan confidence threshold
                filtered_detections = [d for d in detections if d['confidence'] >= confidence_threshold]
                
                # Gambar deteksi pada frame
                frame_with_detections = draw_detections(frame.copy(), filtered_detections)
                
                # Convert BGR ke RGB untuk Streamlit
                frame_rgb = cv2.cvtColor(frame_with_detections, cv2.COLOR_BGR2RGB)
                
                # Tampilkan frame
                video_placeholder.image(frame_rgb, channels="RGB", use_container_width=True)
                
                # Update hasil deteksi
                if filtered_detections:
                    detection_count += 1
                    results_text = "**Deteksi Saat Ini:**\n\n"
                    for det in filtered_detections:
                        results_text += f"• **{det['class']}** ({det['confidence']:.2f})\n"
                        total_detections[det['class']] += 1
                    results_placeholder.markdown(results_text)
                else:
                    results_placeholder.markdown("**Tidak ada deteksi**")
                
                # Update statistik
                stats_text = f"**Total Deteksi:** {detection_count}\n\n"
                stats_text += "**Per Kelas:**\n"
                for class_name, count in total_detections.items():
                    if count > 0:
                        stats_text += f"• {class_name}: {count}\n"
                stats_placeholder.markdown(stats_text)
                
                # Small delay untuk mengurangi CPU usage
                time.sleep(0.1)
            
            cap.release()
            
        except Exception as e:
            st.error(f"❌ Error: {str(e)}")
            st.session_state.camera_active = False
    
    else:
        video_placeholder.markdown("### 📷 Camera tidak aktif\nKlik 'Start Camera' untuk memulai deteksi")
        results_placeholder.markdown("**Menunggu deteksi...**")

if __name__ == "__main__":
    main()
